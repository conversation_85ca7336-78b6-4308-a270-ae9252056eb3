<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Page</title>
    <link rel="icon" type="image/x-icon" href="{{ url_for('static', filename='img/logo.png') }}">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.2/font/bootstrap-icons.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-blue: #004AAD;
            --primary-pink: #CD208B;
            --yellow: #FFD700;
            --text-dark: #000000;
            --text-light: #FFFFFF;
            --text-gray: #666;
        }

        html {
            font-size: 1rem;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Poppins', system-ui, -apple-system, sans-serif;
            transition: all 0.3s ease-in-out;
        }

        body {
            font-family: 'Poppins', system-ui, -apple-system, sans-serif;
            overflow-x: hidden;
        }

        /* Button Styles */
        .btn {
            padding: 0 1.5rem;
            border-radius: 8px;
            font-size: 1rem;
            font-weight: 600;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            min-width: 120px;
            height: 50px;
            cursor: pointer;
        }

        .btn-primary {
            background: var(--primary-pink);
            border: 2px solid var(--primary-pink);
            color: var(--text-light);
        }

        .btn-outline {
            background: white;
            border: 2px solid var(--primary-blue);
            color: var(--primary-blue);
        }

        .btn-gigs {
            background: var(--primary-blue);
            border: 2px solid var(--primary-blue);
            color: var(--text-light);
        }

        .btn-primary:hover {
            background: white;
            border: 2px solid var(--primary-pink);
            color: var(--primary-pink);
        }

        .btn-outline:hover {
            background: var(--primary-blue);
            color: var(--text-light);
        }

        .btn-gigs:hover {
            background: white;
            color: var(--primary-blue);
        }

        .btn-sm {
            padding: 5px 10px;
            font-size: 14px;
            height: auto;
            min-width: auto;
        }

        .btn-danger {
            background: #dc3545;
            border: 2px solid #dc3545;
            color: white;
        }

        .btn-danger:hover {
            background: white;
            color: #dc3545;
        }

        /* Modal Styles */
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
            overflow: hidden;
        }

        .modal-content {
            background-color: #fefefe;
            margin: 2% auto;
            width: 90%;
            max-width: 900px;
            border-radius: 10px;
            position: relative;
            display: flex;
            flex-direction: column;
            max-height: 95vh;
        }

        .modal-header {
            padding: 20px 30px;
            background: white;
            border-bottom: 1px solid #e9ecef;
            border-radius: 10px 10px 0 0;
            position: relative;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal-header h2 {
            margin: 0;
            color: var(--primary-blue);
        }

        .close {
            color: #aaa;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
            padding: 0 10px;
        }

        .close:hover {
            color: var(--primary-pink);
        }

        .modal-body {
            padding: 30px;
            overflow-y: auto;
            max-height: calc(95vh - 80px);
        }

        .modal-buttons {
            display: flex;
            justify-content: flex-end;
            gap: 1rem;
            padding: 20px;
        }

        /* Client Details Modal */
        .client-details {
            margin-top: 20px;
        }

        .profile-section {
            display: flex;
            justify-content: center;
            gap: 40px;
            margin-bottom: 30px;
            flex-wrap: wrap;
        }

        .photo-container {
            text-align: center;
        }

        .photo-label {
            font-weight: bold;
            margin-bottom: 10px;
            color: var(--primary-blue);
        }

        .modal-profile-photo,
        .modal-business-logo {
            width: 120px;
            height: 120px;
            object-fit: cover;
            border-radius: 50%;
            border: 2px solid var(--primary-blue);
        }

        .details-grid {
            display: grid;
            grid-template-columns: 1fr;
            gap: 25px;
        }

        .detail-item {
            padding: 20px;
            background: var(--text-light);
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        .detail-item h3 {
            color: var(--primary-blue);
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 2px solid #e9ecef;
        }

        .info-content p {
            margin: 10px 0;
            line-height: 1.6;
        }

        /* Container Styles */
        .container {
            max-width: 2000px;
            margin: auto;
            padding: 0;
        }

        /* Navbar Styles */
        .navbar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 1rem;
            background: white;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            height: 5.5rem;
            position: relative;
            z-index: 100;
        }

        .navbar-left {
            display: flex;
            align-items: center;
            gap: 1.5rem;
        }

        .logo {
            display: flex;
            align-items: center;
            font-size: 1.7rem;
            color: var(--primary-pink);
            margin-right: 1rem;
        }

        .logo img {
            width: 5rem;
            height: 5rem;
        }

        .logo h1 {
            font-size: 1.7rem;
            margin-left: -0.5rem;
            font-weight: bold;
        }

        .nav-links {
            display: flex;
            gap: 1rem;
            align-items: center;
        }

        .nav-links a {
            color: var(--primary-blue);
            text-decoration: none;
            padding: 0.5rem 1rem;
            font-size: 1.1rem;
            font-weight: 500;
        }

        .nav-links a:hover,
        .nav-links a.active {
            color: var(--primary-pink);
        }

        .navbar-right {
            display: flex;
            align-items: center;
            gap: 1.5rem;
        }

        /* Search container styles */
        .search-container {
            display: flex;
            align-items: center;
            height: 50px;
            margin-right: 1.5rem;
        }

        .search-bar {
            display: flex;
            align-items: center;
            height: 100%;
            background: white;
            border: 2px solid var(--primary-blue);
            border-radius: 0 8px 8px 0;
            width: 200px;
        }

        .search-bar input {
            border: none;
            outline: none;
            padding: 0 1rem;
            width: 100%;
            height: 100%;
            font-size: 1rem;
        }

        .search-bar .icon {
            color: var(--primary-blue);
            padding: 0 0.5rem;
            font-size: 1rem;
        }

        .search-bar:hover .icon {
            color: var(--primary-pink);
        }

        /* Search type dropdown */
        .search-type-select {
            position: relative;
        }

        .search-type-button {
            height: 50px;
            background: white;
            border: 2px solid var(--primary-blue);
            border-right: none;
            border-radius: 8px 0 0 8px;
            padding: 0 1rem;
            color: var(--primary-blue);
            font-size: 1.2rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            cursor: pointer;
        }

        .search-type-button:hover {
            color: var(--primary-pink);
            border-color: var(--primary-pink);
        }

        .search-type-button:after {
            content: '▼';
            font-size: 0.8rem;
        }

        .search-type-dropdown {
            position: absolute;
            top: 55px;
            left: 0;
            background: white;
            border: 2px solid var(--primary-blue);
            border-radius: 8px;
            margin-top: 0.5rem;
            min-width: 120px;
            display: none;
            z-index: 1000;
            width: max-content;
        }

        .search-type-dropdown.active {
            display: block;
        }

        .search-type-option {
            padding: 1rem 1.5rem;
            cursor: pointer;
            color: var(--primary-blue);
        }

        .search-type-option:hover {
            background: #f5f5f5;
            color: var(--primary-pink);
        }

        /* Auth buttons container */
        .auth-buttons {
            display: flex;
            align-items: center;
            gap: 1.5rem;
        }

        /* Notification icon */
        .notification-icon {
            position: relative;
            cursor: pointer;
            padding: 0.5rem;
        }

        .notification-icon i {
            font-size: 1.8rem;
            color: var(--primary-blue);
            transition: color 0.3s ease;
        }

        .notification-icon:hover i {
            color: var(--primary-pink);
        }

        .notification-badge {
            position: absolute;
            top: -5px;
            right: -5px;
            background-color: var(--primary-pink);
            color: white;
            border-radius: 50%;
            padding: 0.2rem 0.5rem;
            font-size: 0.8rem;
            min-width: 20px;
            height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        /* Profile dropdown */
        .profile-dropdown {
            position: relative;
        }

        .profile-button {
            width: 48px;
            height: 48px;
            border-radius: 50%;
            overflow: hidden;
            cursor: pointer;
            border: 2px solid var(--primary-blue);
            transition: border-color 0.3s ease;
        }

        .profile-button:hover {
            border-color: var(--primary-pink);
        }

        .profile-button img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .profile-dropdown-content {
            display: none;
            position: absolute;
            right: 0;
            top: 60px;
            background: white;
            min-width: 220px;
            border-radius: 8px;
            box-shadow: 0 8px 16px rgba(0,0,0,0.1);
            z-index: 1001;
        }

        .profile-dropdown-content a {
            color: var(--primary-blue);
            padding: 12px 16px;
            text-decoration: none;
            display: flex;
            align-items: center;
            gap: 10px;
            font-size: 1rem;
        }

        .profile-dropdown-content a:hover {
            background-color: #f8f9fa;
            color: var(--primary-pink);
        }

        .dropdown-divider {
            height: 1px;
            background-color: #eee;
            margin: 8px 0;
        }

        .logout-option {
            color: #dc3545 !important;
        }

        .logout-option:hover {
            background-color: #fff5f5 !important;
        }

        .profile-dropdown.active .profile-dropdown-content {
            display: block;
        }

        /* Footer Styles */
        footer {
            background: var(--primary-blue);
            padding: 1.5rem;
            text-align: center;
        }

        footer p {
            color: var(--text-light);
            margin: 0;
            font-size: 1rem;
        }

        footer a {
            color: var(--text-light);
            text-decoration: none;
            margin-left: 0.5rem;
        }

        footer a:hover {
            text-decoration: underline;
        }

        /* Table Styles */
        .section {
            padding: 1rem 2rem;
        }

        .section h1 {
            color: var(--primary-blue);
            justify-content: center;
            align-items: center;
            text-align: center;
            margin: 1rem;
        }

        .section h2 {
            color: var(--primary-pink);
            margin: 1rem 0;
        }

        .table-responsive {
            overflow-x: auto;
            margin-bottom: 2rem;
            max-height: 500px;
            overflow-y: auto;
        }

        .table {
            width: 100%;
            border-collapse: collapse;
            border: 1px solid #dee2e6;
            border-radius: 8px;
        }

        .table th, .table td {
            padding: 12px;
            text-align: center;
            border: 1px solid #dee2e6;
        }

        .table th {
            background-color: var(--primary-blue);
            color: white;
            position: sticky;
            top: 0;
        }

        .table tbody tr:nth-child(even) {
            background-color: #f9f9f9;
        }

        .table tbody tr:hover {
            background-color: #f1f1f1;
        }

        /* Management Controls Styles */
        .management-controls {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin: 1rem 0;
            gap: 2rem;
            flex-wrap: wrap;
        }

        .filter-controls {
            display: flex;
            align-items: center;
            gap: 1rem;
            flex-wrap: wrap;
        }

        .filter-section {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .filter-section label {
            color: var(--primary-blue);
            font-weight: 500;
            white-space: nowrap;
        }

        .time-filter-dropdown {
            padding: 0.5rem 1rem;
            border: 2px solid var(--primary-blue);
            border-radius: 8px;
            background: white;
            color: var(--primary-blue);
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: 500;
            min-width: 150px;
        }

        .time-filter-dropdown:hover {
            border-color: var(--primary-pink);
            color: var(--primary-pink);
        }

        .search-section {
            display: flex;
            align-items: center;
            position: relative;
            flex-grow: 1;
            max-width: 400px;
        }

        .search-input {
            width: 100%;
            padding: 0.5rem 2.5rem 0.5rem 1rem;
            border: 2px solid var(--primary-blue);
            border-radius: 8px;
            font-size: 1rem;
            transition: all 0.3s ease;
        }

        .search-input:hover {
            border-color: var(--primary-pink);
        }

        .search-icon {
            position: absolute;
            right: 1rem;
            color: var(--primary-blue);
            transition: color 0.3s ease;
        }

        /* Table Highlight Styles */
        .created-at-highlight {
            background-color: #f0f7ff !important;
            font-weight: 500;
            color: var(--primary-blue) !important;
        }

        .name-highlight,
        .business-name-highlight {
            background-color: #fff0f7 !important;
            font-weight: 500;
            color: var(--primary-pink) !important;
        }

        /* Status Select Dropdown Styling */
        .status-select {
            padding: 0.5rem;
            border: 2px solid var(--primary-blue);
            border-radius: 6px;
            background: white;
            cursor: pointer;
            font-size: 0.9rem;
            min-width: 120px;
            transition: all 0.3s ease;
        }

        .status-select:hover {
            border-color: var(--primary-pink);
        }

        .status-select option {
            padding: 10px;
        }

        .status-select option[value="pending"] {
            color: #f0ad4e;
        }

        .status-select option[value="approved"] {
            color: #5cb85c;
        }

        .status-select option[value="declined"] {
            color: #d9534f;
        }

        .status-select option[value="deleted"] {
            color: #dc3545;
        }

        .status-select.pending {
            color: #f0ad4e;
            border-color: #f0ad4e;
        }

        .status-select.approved {
            color: #5cb85c;
            border-color: #5cb85c;
        }

        .status-select.declined {
            color: #d9534f;
            border-color: #d9534f;
        }

        .status-select.deleted {
            color: #dc3545;
            border-color: #dc3545;
        }

        /* Document Link Styling */
        .document-link {
            display: inline-flex;
            align-items: center;
            padding: 8px 16px;
            background-color: var(--primary-pink);
            color: var(--text-light);
            border-radius: 6px;
            text-decoration: none;
            transition: all 0.3s ease;
            font-size: 0.9rem;
            gap: 8px;
            border: 2px solid var(--primary-pink);
        }

        .document-link:hover {
            background-color: white;
            color: var(--primary-pink);
        }

        .document-link i {
            font-size: 1.1rem;
        }

        /* Document preview container */
        .document-preview {
            margin-top: 10px;
            padding: 12px;
            background-color: #f8f9fa;
            border-radius: 6px;
            border: 1px solid #e9ecef;
            display: flex;
            justify-content: flex-start;
        }

        /* Profile thumbnail */
        .profile-thumbnail {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            object-fit: cover;
        }

        /* Hamburger Menu Styles */
        .hamburger {
            display: none;
            cursor: pointer;
            padding: 15px;
            background: none;
            border: none;
            z-index: 1001;
        }

        .hamburger span {
            display: block;
            width: 25px;
            height: 3px;
            margin: 5px 0;
            background-color: var(--primary-blue);
            transition: all 0.3s ease;
        }

        /* Side nav styles */
        .side-nav {
            position: fixed;
            top: 0;
            left: -100%;
            height: 100vh;
            width: 280px;
            background-color: white;
            z-index: 1000;
            transition: left 0.3s ease;
            box-shadow: 2px 0 10px rgba(0,0,0,0.1);
        }

        .side-nav.active {
            left: 0;
        }

        .side-nav-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(0,0,0,0.5);
            z-index: 999;
            display: none;
        }

        .side-nav-overlay.active {
            display: block;
        }

        .side-nav-content {
            padding: 80px 0 20px 0;
            height: 100%;
            display: flex;
            flex-direction: column;
        }

        .nav-items {
            display: flex;
            flex-direction: column;
            width: 100%;
        }

        .nav-item {
            padding: 15px 25px;
            color: var(--primary-blue);
            text-decoration: none;
            font-size: 1.1rem;
            transition: all 0.3s ease;
            border-bottom: 1px solid #f0f0f0;
        }

        .nav-item:hover {
            background-color: #f8f9fa;
            color: var(--primary-pink);
        }

        /* Responsive Breakpoints */
        @media (max-width: 1420px) {
            .search-container {
                display: none;
            }

            .navbar-right {
                gap: 1rem;
            }
        }

        @media (max-width: 1200px) {
            .dashboard-grid {
                grid-template-columns: repeat(2, 1fr);
            }

            .chart-card {
                grid-column: span 1;
            }
        }

        @media (max-width: 1100px) {
            .nav-links {
                display: none;
            }

            .hamburger {
                display: block;
            }

            .logo {
                margin-left: 0.5rem;
            }
        }

        @media (max-width: 768px) {
            .dashboard-grid {
                grid-template-columns: 1fr;
            }

            .stat-card {
                padding: 1rem;
            }

            .management-controls {
                flex-direction: column;
                align-items: flex-start;
            }

            .search-section {
                max-width: 100%;
                width: 100%;
            }

            .section {
                padding: 1rem;
            }

            footer {
                padding: 1rem;
            }

            footer p {
                font-size: 0.9rem;
            }
        }

        @media (max-width: 480px) {
            .stat-number {
                font-size: 1.5rem;
            }

            .profile-section {
                flex-direction: column;
                gap: 20px;
            }

            .modal-profile-photo,
            .modal-business-logo {
                width: 100px;
                height: 100px;
            }

            .table th, .table td {
                padding: 8px;
                font-size: 0.9rem;
            }

            .btn {
                min-width: auto;
                padding: 0 1rem;
                height: 40px;
                font-size: 0.9rem;
            }

            footer {
                padding: 0.8rem;
            }

            footer p {
                font-size: 0.8rem;
            }
        }

        @media (max-width: 400px) {
            .logo h1 {
                display: none;
            }

            .logo img {
                width: 4rem;
                height: 4rem;
            }

            .table th, .table td {
                padding: 6px;
                font-size: 0.8rem;
            }

            .status-select {
                min-width: 100px;
                font-size: 0.8rem;
            }
        }
    </style>
</head>
<body>
    <!-- Side Navigation Panel -->
    <div class="side-nav" id="sideNav">
        <div class="side-nav-content">
            <div class="nav-items">
                <a href="javascript:void(0)" onclick="showTab('Dashboard')" class="nav-item">Dashboard</a>
                <a href="javascript:void(0)" onclick="showTab('Clients')" class="nav-item">Clients</a>
                <a href="javascript:void(0)" onclick="showTab('Geniuses')" class="nav-item">Geniuses</a>
                <a href="javascript:void(0)" onclick="showTab('Gigs')" class="nav-item">Gigs</a>
                <a href="javascript:void(0)" onclick="showTab('Earnings')" class="nav-item">Earnings</a>
                <a href="javascript:void(0)" onclick="showTab('Reports')" class="nav-item">Reports</a>
            </div>
        </div>
    </div>
    <div class="side-nav-overlay" id="sideNavOverlay" onclick="toggleMenu()"></div>

    <!-- Main Content -->
    <div class="container">
        <!-- Navbar -->
        <nav class="navbar">
            <div style="display: flex; align-items: center;">
                <button class="hamburger" onclick="toggleMenu()">
                    <span></span>
                    <span></span>
                    <span></span>
                </button>
                <div class="logo">
                    <img src="{{ url_for('static', filename='img/giggenius_logo.jpg') }}" alt="GigGenius Logo">
                    <h1>GigGenius</h1>
                </div>
                <div class="nav-links">
                    <a href="javascript:void(0)" onclick="showTab('Dashboard')" class="active">Dashboard</a>
                    <a href="javascript:void(0)" onclick="showTab('Clients')">Clients</a>
                    <a href="javascript:void(0)" onclick="showTab('Geniuses')">Geniuses</a>
                    <a href="javascript:void(0)" onclick="showTab('Gigs')">Gigs</a>
                    <a href="javascript:void(0)" onclick="showTab('Earnings')">Earnings</a>
                    <a href="javascript:void(0)" onclick="showTab('Reports')">Reports</a>
                </div>
            </div>
            <div class="navbar-right">
                <div class="search-container">
                    <div class="search-type-select">
                        <button class="search-type-button" id="searchTypeBtn">
                            <span id="selectedSearchType">All</span>
                        </button>
                        <div class="search-type-dropdown" id="searchTypeDropdown">
                            <div class="search-type-option" data-value="all">All</div>
                            <div class="search-type-option" data-value="genius">Clients</div>
                            <div class="search-type-option" data-value="client">Geniuses</div>
                            <div class="search-type-option" data-value="client">Gigs</div>
                        </div>
                    </div>
                    <div class="search-bar">
                        <input type="text" id="searchInput" placeholder="Search...">
                        <i class="fas fa-search icon"></i>
                    </div>
                </div>
                <div class="auth-buttons">
                    <div class="notification-icon">
                        <i class="fas fa-bell"></i>
                        <span class="notification-badge">3</span>
                    </div>
                    <div class="profile-dropdown">
                        <div class="profile-button">
                            <img src="{{ admin.profile_picture_url if admin else '/static/img/logo.png' }}" alt="Admin Profile">
                        </div>
                        <div class="profile-dropdown-content">
                            <a href="{{ url_for('landing_page') }}">
                                <i class="fas fa-user"></i>
                                My Profile
                            </a>
                            <a href="{{ url_for('landing_page') }}">
                                <i class="fas fa-cog"></i>
                                Account Settings
                            </a>
                            <div class="dropdown-divider"></div>
                            <a href="javascript:void(0)" onclick="openLogoutModal()" class="logout-option">
                                <i class="fas fa-sign-out-alt"></i>
                                Log Out
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </nav>

        <main>
            <!-- Dashboard Tab -->
            <div class="tab-content" id="DashboardTab">
                <div class="section">
                    <h1>Welcome to the Dashboard Admin</h1>
                </div>
            </div>

            <!-- Clients Tab -->
            <div class="tab-content" id="ClientsTab" style="display: none;">
                <div class="section">
                    <h1>Clients Registration Management</h1>
                    <div class="management-controls">
                        <div class="filter-controls">
                            <div class="filter-section">
                                <label>Filter by Registration Date:</label>
                                <select class="time-filter-dropdown" id="registrationFilter">
                                    <option value="daily">Daily</option>
                                    <option value="weekly">Weekly</option>
                                    <option value="monthly">Monthly</option>
                                    <option value="yearly">Yearly</option>
                                </select>
                            </div>
                            <div class="search-section">
                                <input type="text" class="search-input" id="clientSearchInput" placeholder="Search by name or business name...">
                                <i class="fas fa-search search-icon"></i>
                            </div>
                        </div>
                    </div>
                    <h2>Register Clients</h2>
                    <div class="table-responsive">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>Name</th>
                                    <th>Log-in Email</th>
                                    <th>Job Title</th>
                                    <th>Business Name</th>
                                    <th>PWD Status</th>
                                    <th>Commission</th>
                                    <th>Created At</th>
                                    <th>View Details</th>
                                    <th>Action</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for client in register_clients %}
                                <tr>
                                    <td>{{ client.id }}</td>
                                    <td>{{ client.first_name }} {{ client.last_name }}</td>
                                    <td>{{ client.work_email }}</td>
                                    <td>{{ client.position }}</td>
                                    <td>{{ client.business_name }}</td>
                                    <td>
                                        {% if client.is_pwd %}
                                            <span class="badge badge-info">PWD</span>
                                        {% else %}
                                            <span class="badge badge-secondary">Regular</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <span class="commission-rate">{{ client.commission_rate }}%</span>
                                    </td>
                                    <td>{{ client.created_at.strftime('%Y-%m-%d') }}</td>
                                    <td>
                                        <button class="btn btn-primary btn-sm" onclick="viewClientDetails({{ client.id }})">View</button>
                                    </td>
                                    <td>
                                        <select class="status-select" onchange="updateClientStatus({{ client.id }}, this.value)">
                                            <option value="pending" {% if client.status == 'pending' %}selected{% endif %}>Pending</option>
                                            <option value="approved" {% if client.status == 'approved' %}selected{% endif %}>Approved</option>
                                            <option value="declined" {% if client.status == 'declined' %}selected{% endif %}>Declined</option>
                                            <option value="deleted" {% if client.status == 'deleted' %}selected{% endif %}>Delete</option>
                                        </select>
                                    </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>

                        <h2 class="mt-5">Approved Clients</h2>
                        <div class="table-responsive">
                            <table class="table">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>Name</th>
                                        <th>Log-in Email</th>
                                        <th>Job Title</th>
                                        <th>Business Name</th>
                                        <th>PWD Status</th>
                                        <th>Commission</th>
                                        <th>Created At</th>
                                        <th>View Details</th>
                                        <th>Action</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for client in approve_clients %}
                                    <tr>
                                        <td>{{ client.id }}</td>
                                        <td>{{ client.first_name }} {{ client.last_name }}</td>
                                        <td>{{ client.work_email }}</td>
                                        <td>{{ client.position }}</td>
                                        <td>{{ client.business_name }}</td>
                                        <td>
                                            {% if client.is_pwd %}
                                                <span class="badge badge-info">PWD</span>
                                            {% else %}
                                                <span class="badge badge-secondary">Regular</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            <span class="commission-rate">{{ client.commission_rate }}%</span>
                                        </td>
                                        <td>{{ client.created_at.strftime('%Y-%m-%d') }}</td>
                                        <td>
                                            <button class="btn btn-primary btn-sm" onclick="viewClientDetails({{ client.id }})">View</button>
                                        </td>
                                        <td>
                                            <select class="status-select" onchange="updateClientStatus({{ client.id }}, this.value)">
                                                <option value="approved" selected>Approved</option>
                                                <option value="declined">Declined</option>
                                                <option value="deleted">Delete</option>
                                            </select>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- Geniuses Tab -->
                <div class="tab-content" id="GeniusesTab" style="display: none;">
                    <div class="section">
                        <h1>Geniuses Registration Management</h1>
                        <div class="management-controls">
                            <div class="filter-controls">
                                <div class="filter-section">
                                    <label>Filter by Registration Date:</label>
                                    <select class="time-filter-dropdown" id="registrationFilter">
                                        <option value="daily">Daily</option>
                                        <option value="weekly">Weekly</option>
                                        <option value="monthly">Monthly</option>
                                        <option value="yearly">Yearly</option>
                                    </select>
                                </div>
                                <div class="search-section">
                                    <input type="text" class="search-input" id="clientSearchInput" placeholder="Search by name or business name...">
                                    <i class="fas fa-search search-icon"></i>
                                </div>
                            </div>
                        </div>
                        <h2>Register Geniuses</h2>
                        <div class="table-responsive">
                            <table class="table">
                                <thead>
                                    <tr>
                                        <th>Name</th>
                                        <th>Country</th>
                                        <th>PWD Status</th>
                                        <th>Commission</th>
                                        <th>Actions</th>
                                        <th>Status</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for genius in register_geniuses %}
                                    <tr>
                                        <td>{{ genius.first_name }} {{ genius.last_name }}</td>
                                        <td>{{ genius.country }}</td>
                                        <td>
                                            {% if genius.is_pwd %}
                                                <span class="badge badge-info">PWD</span>
                                            {% else %}
                                                <span class="badge badge-secondary">Regular</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            <span class="commission-rate">{{ genius.commission_rate }}%</span>
                                        </td>
                                        <td>
                                            <button class="btn btn-primary btn-sm" onclick="viewGenius({{ genius.id }})">View</button>
                                        </td>
                                        <td>
                                            <select class="status-select" onchange="updateStatus({{ genius.id }}, this.value)">
                                                <option value="pending" {% if genius.status == 'pending' %}selected{% endif %}>Pending</option>
                                                <option value="approved" {% if genius.status == 'approved' %}selected{% endif %}>Approved</option>
                                                <option value="declined" {% if genius.status == 'declined' %}selected{% endif %}>Declined</option>
                                            </select>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>

                        <h2 class="mt-5">Approved Geniuses</h2>
                        <div class="table-responsive">
                            <table class="table">
                                <thead>
                                    <tr>
                                        <th>Name</th>
                                        <th>Country</th>
                                        <th>Position</th>
                                        <th>Expertise</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for genius in approve_geniuses %}
                                    <tr>
                                        <td>{{ genius.first_name }} {{ genius.last_name }}</td>
                                        <td>{{ genius.country }}</td>
                                        <td>{{ genius.position }}</td>
                                        <td>{{ genius.expertise }}</td>
                                        <td>
                                            <button class="btn btn-primary btn-sm" onclick="viewApprovedGenius({{ genius.id }})">View</button>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- Gigs Tab -->
                <div class="tab-content" id="GigsTab" style="display: none;">
                    <div class="section">
                        <h1>Gigs Management</h1>
                        <div class="management-controls">
                            <div class="filter-section">
                                <label>Filter by Status:</label>
                                <select class="time-filter-dropdown" id="gigStatusFilter">
                                    <option value="all">All Gigs</option>
                                    <option value="active">Active</option>
                                    <option value="pending">Pending Review</option>
                                    <option value="completed">Completed</option>
                                </select>
                            </div>
                            <div class="search-section">
                                <input type="text" class="search-input" id="gigSearchInput" placeholder="Search gigs...">
                                <i class="fas fa-search search-icon"></i>
                            </div>
                        </div>
                        <div class="table-responsive">
                            <table class="table">
                                <thead>
                                    <tr>
                                        <th>Title</th>
                                        <th>Client</th>
                                        <th>Budget</th>
                                        <th>Posted Date</th>
                                        <th>Status</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for gig in gigs %}
                                    <tr>
                                        <td>{{ gig.title }}</td>
                                        <td>{{ gig.client_name }}</td>
                                        <td>${{ gig.budget }}</td>
                                        <td>{{ gig.posted_date }}</td>
                                        <td>
                                            <span class="status-badge {{ gig.status }}">{{ gig.status }}</span>
                                        </td>
                                        <td>
                                            <button class="btn btn-primary btn-sm" onclick="viewGig({{ gig.id }})">View</button>
                                            <button class="btn btn-danger btn-sm" onclick="confirmDeleteGig({{ gig.id }})">Delete</button>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- Earnings Tab -->
                <div class="tab-content" id="EarningsTab" style="display: none;">
                    <div class="section">
                        <h1>Platform Earnings</h1>
                        <div class="dashboard-grid">
                            <div class="stat-card">
                                <h3>Total Earnings</h3>
                                <p class="stat-number">${{ total_earnings }}</p>
                                <p class="stat-trend">↑ 8% from last period</p>
                            </div>
                            <div class="stat-card">
                                <h3>Monthly Revenue</h3>
                                <p class="stat-number">${{ monthly_revenue }}</p>
                                <p class="stat-trend">↑ 5% from last month</p>
                            </div>
                            <div class="stat-card">
                                <h3>Completed Transactions</h3>
                                <p class="stat-number">{{ completed_transactions }}</p>
                            </div>
                        </div>

                        <div class="chart-card earnings">
                            <h3>Earnings Overview</h3>
                            <canvas id="earningsChart"></canvas>
                        </div>

                        <div class="table-responsive mt-4">
                            <h3>Recent Transactions</h3>
                            <table class="table">
                                <thead>
                                    <tr>
                                        <th>Transaction ID</th>
                                        <th>Date</th>
                                        <th>Client</th>
                                        <th>Genius</th>
                                        <th>Amount</th>
                                        <th>Status</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for transaction in recent_transactions %}
                                    <tr>
                                        <td>{{ transaction.id }}</td>
                                        <td>{{ transaction.date }}</td>
                                        <td>{{ transaction.client_name }}</td>
                                        <td>{{ transaction.genius_name }}</td>
                                        <td>${{ transaction.amount }}</td>
                                        <td>{{ transaction.status }}</td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- Reports Tab -->
                <div class="tab-content" id="ReportsTab" style="display: none;">
                    <div class="section">
                        <h1>User Reports</h1>

                        <!-- Reported Clients Section -->
                        <div class="reports-section">
                            <h3>Reported Clients</h3>
                            <div class="table-responsive">
                                <table class="table">
                                    <thead>
                                        <tr>
                                            <th>Reported Client</th>
                                            <th>Reported By</th>
                                            <th>Reason</th>
                                            <th>Date Reported</th>
                                            <th>Status</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for report in client_reports %}
                                        <tr>
                                            <td>{{ report.client_name }}</td>
                                            <td>{{ report.genius_name }}</td>
                                            <td>{{ report.reason }}</td>
                                            <td>{{ report.report_date }}</td>
                                            <td>
                                                <select class="status-select" onchange="updateReportStatus({{ report.id }}, this.value)">
                                                    <option value="pending" {% if report.status == 'pending' %}selected{% endif %}>Pending</option>
                                                    <option value="investigating" {% if report.status == 'investigating' %}selected{% endif %}>Investigating</option>
                                                    <option value="resolved" {% if report.status == 'resolved' %}selected{% endif %}>Resolved</option>
                                                    <option value="dismissed" {% if report.status == 'dismissed' %}selected{% endif %}>Dismissed</option>
                                                </select>
                                            </td>
                                            <td>
                                                <button class="btn btn-primary btn-sm" onclick="viewReport({{ report.id }})">View Details</button>
                                                <button class="btn btn-danger btn-sm" onclick="confirmDeleteReport({{ report.id }})">Delete</button>
                                            </td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        </div>

                        <!-- Reported Geniuses Section -->
                        <div class="reports-section mt-5">
                            <h3>Reported Geniuses</h3>
                            <div class="table-responsive">
                                <table class="table">
                                    <thead>
                                        <tr>
                                            <th>Reported Genius</th>
                                            <th>Reported By</th>
                                            <th>Reason</th>
                                            <th>Date Reported</th>
                                            <th>Status</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for report in genius_reports %}
                                        <tr>
                                            <td>{{ report.genius_name }}</td>
                                            <td>{{ report.client_name }}</td>
                                            <td>{{ report.reason }}</td>
                                            <td>{{ report.report_date }}</td>
                                            <td>
                                                <select class="status-select" onchange="updateReportStatus({{ report.id }}, this.value)">
                                                    <option value="pending" {% if report.status == 'pending' %}selected{% endif %}>Pending</option>
                                                    <option value="investigating" {% if report.status == 'investigating' %}selected{% endif %}>Investigating</option>
                                                    <option value="resolved" {% if report.status == 'resolved' %}selected{% endif %}>Resolved</option>
                                                    <option value="dismissed" {% if report.status == 'dismissed' %}selected{% endif %}>Dismissed</option>
                                                </select>
                                            </td>
                                            <td>
                                                <button class="btn btn-primary btn-sm" onclick="viewReport({{ report.id }})">View Details</button>
                                                <button class="btn btn-danger btn-sm" onclick="confirmDeleteReport({{ report.id }})">Delete</button>
                                            </td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
        </main>

            <!-- Footer -->
            <footer>
                <p>©2025 GigGenius by<a href="https://genuinelysolutions.com/">Genuinely Business Solutions</a></p>
            </footer>
        </div>

        <!-- Modal for Logout -->
        <div id="logoutModal" class="modal">
            <div class="modal-content">
                <div class="modal-header">
                    <h2>Confirm Logout</h2>
                    <span class="close" onclick="closeLogoutModal()">&times;</span>
                </div>
                <div class="modal-body">
                    <p>Are you sure you want to logout?</p>
                </div>
                <div class="modal-buttons">
                    <button onclick="logout()" class="btn btn-outline">Yes</button>
                    <button onclick="closeLogoutModal()" class="btn btn-primary">Cancel</button>
                </div>
            </div>
        </div>

        <!-- Modal for Delete Confirmation -->
        <div id="deleteModal" class="modal">
            <div class="modal-content">
                <div class="modal-header">
                    <h2>Confirm Delete</h2>
                    <span class="close" onclick="closeDeleteModal()">&times;</span>
                </div>
                <div class="modal-body">
                    <p id="deleteModalMessage">Are you sure you want to delete this item?</p>
                </div>
                <div class="modal-buttons">
                    <button onclick="confirmDelete()" class="btn btn-danger">Delete</button>
                    <button onclick="closeDeleteModal()" class="btn btn-primary">Cancel</button>
                </div>
            </div>
        </div>

        <!-- Client Details Modal -->
        <div id="clientDetailsModal" class="modal">
            <div class="modal-content">
                <div class="modal-header">
                    <h2>Client Details</h2>
                    <span class="close" onclick="closeClientModal()">&times;</span>
                </div>

                <div class="modal-body">
                    <div class="client-details">
                        <div class="profile-section">
                            <div class="photo-container">
                                <img id="modalProfilePhoto" src="" alt="Profile Photo" class="modal-profile-photo">
                                <p class="photo-label">Profile Photo</p>
                            </div>
                            <div class="photo-container">
                                <img id="modalBusinessLogo" src="" alt="Business Logo" class="modal-business-logo">
                                <p class="photo-label">Business Logo</p>
                            </div>
                        </div>

                        <div class="details-grid">
                            <div class="detail-item personal-info">
                                <h3>Personal Information</h3>
                                <div class="info-content">
                                    <p><strong>Name:</strong> <span id="modalName"></span></p>
                                    <p><strong>Birthday:</strong> <span id="modalBirthday"></span></p>
                                    <p><strong>Job Title:</strong> <span id="modalPosition"></span></p>
                                    <p><strong>Country:</strong> <span id="modalCountry"></span></p>
                                    <p><strong>Mobile:</strong> <span id="modalMobile"></span></p>
                                    <p><strong>Work Email:</strong> <span id="modalWorkEmail"></span></p>
                                </div>
                            </div>

                            <div class="detail-item business-info">
                                <h3>Business Details</h3>
                                <div class="info-content">
                                    <p><strong>Business Name:</strong> <span id="modalBusinessName"></span></p>
                                    <p><strong>Business Address:</strong> <span id="modalBusinessAddress"></span></p>
                                    <p><strong>Business Email:</strong> <span id="modalBusinessEmail"></span></p>
                                    <p><strong>Industry:</strong> <span id="modalIndustry"></span></p>
                                    <p><strong>Website:</strong> <span id="modalWebsite"></span></p>
                                    <p><strong>Employees:</strong> <span id="modalEmployeeCount"></span></p>
                                </div>
                            </div>

                            <div class="detail-item verification-docs">
                                <h3>Verification Documents</h3>
                                <div class="info-content">
                                    <p><strong>Business Registration:</strong></p>
                                    <div class="document-preview">
                                        <a id="modalBusinessDoc" href="#" target="_blank" class="document-link">
                                            <i class="fas fa-file-pdf"></i>
                                            View Document
                                        </a>
                                    </div>
                                </div>
                            </div>

                            <div class="detail-item pwd-info">
                                <h3>PWD Information</h3>
                                <div class="info-content">
                                    <p><strong>PWD Status:</strong> <span id="modalPwdStatus"></span></p>
                                    <p><strong>Commission Rate:</strong> <span id="modalCommissionRate"></span></p>
                                    <div id="modalPwdDetails" style="display: none;">
                                        <p><strong>Condition:</strong> <span id="modalPwdCondition"></span></p>
                                        <p><strong>PWD Proof Document:</strong></p>
                                        <div class="document-preview">
                                            <a id="modalPwdProof" href="#" target="_blank" class="document-link">
                                                <i class="fas fa-file-pdf"></i>
                                                View PWD Proof
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
        <script>
            // Global variables for delete functionality
            let currentDeleteId = null;
            let currentDeleteType = null;
            let currentDeleteCallback = null;

            // Navbar and Side Menu
            function toggleMenu() {
                const sideNav = document.getElementById('sideNav');
                const overlay = document.getElementById('sideNavOverlay');
                const hamburger = document.querySelector('.hamburger');
                const spans = hamburger.getElementsByTagName('span');

                sideNav.classList.toggle('active');
                overlay.classList.toggle('active');

                if (sideNav.classList.contains('active')) {
                    spans[0].style.transform = 'rotate(45deg) translate(5px, 5px)';
                    spans[1].style.opacity = '0';
                    spans[2].style.transform = 'rotate(-45deg) translate(7px, -7px)';
                    document.body.style.overflow = 'hidden';
                } else {
                    spans[0].style.transform = 'none';
                    spans[1].style.opacity = '1';
                    spans[2].style.transform = 'none';
                    document.body.style.overflow = 'auto';
                }
            }

            // Close side nav when clicking outside
            document.addEventListener('click', (e) => {
                const sideNav = document.getElementById('sideNav');
                const hamburger = document.querySelector('.hamburger');

                if (sideNav.classList.contains('active') &&
                    !sideNav.contains(e.target) &&
                    !hamburger.contains(e.target)) {
                    toggleMenu();
                }
            });

            // Tab Navigation
            function showTab(tabName) {
                document.querySelectorAll('.tab-content').forEach(tab => {
                    tab.style.display = 'none';
                });

                document.getElementById(tabName + 'Tab').style.display = 'block';

                document.querySelectorAll('.nav-links a, .nav-item').forEach(link => {
                    link.classList.remove('active');
                    if (link.textContent === tabName || link.getAttribute('onclick').includes(tabName)) {
                        link.classList.add('active');
                    }
                });
            }

            // Profile Dropdown
            document.addEventListener('DOMContentLoaded', function() {
                const profileDropdown = document.querySelector('.profile-dropdown');
                const profileButton = profileDropdown.querySelector('.profile-button');

                profileButton.addEventListener('click', function(e) {
                    e.stopPropagation();
                    profileDropdown.classList.toggle('active');
                });

                document.addEventListener('click', function(e) {
                    if (!profileDropdown.contains(e.target)) {
                        profileDropdown.classList.remove('active');
                    }
                });
            });

            // Search Type Dropdown
            document.addEventListener('DOMContentLoaded', function() {
                const searchTypeBtn = document.getElementById('searchTypeBtn');
                const searchTypeDropdown = document.getElementById('searchTypeDropdown');
                const selectedSearchType = document.getElementById('selectedSearchType');
                const searchInput = document.getElementById('searchInput');
                const options = document.querySelectorAll('.search-type-option');

                searchTypeBtn.addEventListener('click', function() {
                    searchTypeDropdown.classList.toggle('active');
                });

                options.forEach(option => {
                    option.addEventListener('click', function() {
                        const value = this.dataset.value;
                        selectedSearchType.textContent = this.textContent;
                        searchTypeDropdown.classList.remove('active');
                        const placeholders = {
                            genius: 'Search for genius...',
                            gigs: 'Search for gigs...',
                            projects: 'Search for projects...',
                            all: 'Search...'
                        };
                        searchInput.placeholder = placeholders[value] || placeholders.all;
                    });
                });

                document.addEventListener('click', function(event) {
                    if (!event.target.closest('.search-type-select')) {
                        searchTypeDropdown.classList.remove('active');
                    }
                });
            });

            // Client Details Modal
            function viewClientDetails(clientId) {
                fetch(`/get_client_details/${clientId}`)
                    .then(response => {
                        if (!response.ok) {
                            throw new Error('Network response was not ok');
                        }
                        return response.json();
                    })
                    .then(client => {
                        if (client.error) {
                            throw new Error(client.error);
                        }

                        const defaultProfilePhoto = "/static/img/default_profile.png";
                        const defaultBusinessLogo = "/static/img/default_business_logo.png";

                        document.getElementById('modalProfilePhoto').src = client.profile_photo || defaultProfilePhoto;
                        document.getElementById('modalBusinessLogo').src = client.business_logo || defaultBusinessLogo;

                        document.getElementById('modalName').textContent = `${client.first_name} ${client.last_name}`;
                        document.getElementById('modalBirthday').textContent = client.birthday || 'Not provided';
                        document.getElementById('modalPosition').textContent = client.position || 'Not provided';
                        document.getElementById('modalCountry').textContent = client.country || 'Not provided';
                        document.getElementById('modalMobile').textContent = client.mobile || 'Not provided';
                        document.getElementById('modalWorkEmail').textContent = client.work_email || 'Not provided';
                        document.getElementById('modalBusinessName').textContent = client.business_name || 'Not provided';
                        document.getElementById('modalBusinessAddress').textContent = client.business_address || 'Not provided';
                        document.getElementById('modalBusinessEmail').textContent = client.business_email || 'Not provided';
                        document.getElementById('modalIndustry').textContent = client.industry || 'Not provided';
                        document.getElementById('modalWebsite').textContent = client.business_website || 'Not provided';
                        document.getElementById('modalEmployeeCount').textContent = client.employee_count || 'Not provided';

                        // PWD Information
                        const pwdStatus = client.is_pwd ? 'Person with Disability (PWD)' : 'Regular User';
                        const pwdBadgeClass = client.is_pwd ? 'badge badge-info' : 'badge badge-secondary';
                        document.getElementById('modalPwdStatus').innerHTML = `<span class="${pwdBadgeClass}">${pwdStatus}</span>`;
                        document.getElementById('modalCommissionRate').textContent = `${client.commission_rate}%`;

                        const pwdDetailsDiv = document.getElementById('modalPwdDetails');
                        if (client.is_pwd) {
                            document.getElementById('modalPwdCondition').textContent = client.pwd_condition || 'Not provided';

                            const pwdProofLink = document.getElementById('modalPwdProof');
                            const pwdProofContainer = pwdProofLink.parentElement;

                            if (client.pwd_proof) {
                                pwdProofLink.href = `data:application/pdf;base64,${client.pwd_proof}`;
                                pwdProofLink.setAttribute('download', 'pwd_proof.pdf');
                                pwdProofContainer.style.display = 'block';

                                pwdProofLink.onclick = function(e) {
                                    e.preventDefault();
                                    const newWindow = window.open();
                                    newWindow.document.write(`
                                        <iframe
                                            src="data:application/pdf;base64,${client.pwd_proof}"
                                            width="100%"
                                            height="100%"
                                            style="border: none;">
                                        </iframe>
                                    `);
                                };
                            } else {
                                pwdProofContainer.style.display = 'none';
                            }

                            pwdDetailsDiv.style.display = 'block';
                        } else {
                            pwdDetailsDiv.style.display = 'none';
                        }

                        const businessDocLink = document.getElementById('modalBusinessDoc');
                        const businessDocContainer = businessDocLink.parentElement;

                        if (client.business_registration_doc) {
                            businessDocLink.href = client.business_registration_doc;
                            businessDocLink.setAttribute('download', 'business_registration.pdf');
                            businessDocContainer.style.display = 'block';

                            businessDocLink.onclick = function(e) {
                                e.preventDefault();
                                const newWindow = window.open();
                                newWindow.document.write(`
                                    <iframe
                                        src="${client.business_registration_doc}"
                                        width="100%"
                                        height="100%"
                                        style="border: none;">
                                    </iframe>
                                `);
                            };
                        } else {
                            businessDocContainer.style.display = 'none';
                        }

                        document.getElementById('clientDetailsModal').style.display = 'block';
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        alert('Failed to load client details. Please try again.');
                    });
            }

            function closeClientModal() {
                document.getElementById('clientDetailsModal').style.display = 'none';
            }

            // Status Updates
            function updateClientStatus(id, status) {
                if (status === 'deleted') {
                    showDeleteConfirmation(id, 'client', 'Are you sure you want to delete this client?', deleteClient);
                    return;
                }

                fetch('/update_client_status', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        client_id: id,
                        status: status
                    })
                })
                .then(response => {
                    if (!response.ok) {
                        return response.json().then(data => {
                            throw new Error(data.error || 'Failed to update status');
                        });
                    }
                    return response.json();
                })
                .then(data => {
                    if (data.success) {
                        location.reload();
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert(error.message);
                    location.reload();
                });
            }

            function updateStatus(id, status) {
                fetch('/update_genius_status', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        genius_id: id,
                        status: status
                    })
                })
                .then(response => {
                    if (!response.ok) {
                        return response.json().then(data => {
                            throw new Error(data.error || 'Failed to update status');
                        });
                    }
                    return response.json();
                })
                .then(data => {
                    if (data.success) {
                        location.reload();
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert(error.message);
                    location.reload();
                });
            }

            function updateReportStatus(id, status) {
                fetch('/update_report_status', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        report_id: id,
                        status: status
                    })
                })
                .then(response => {
                    if (!response.ok) {
                        return response.json().then(data => {
                            throw new Error(data.error || 'Failed to update report status');
                        });
                    }
                    return response.json();
                })
                .then(data => {
                    if (data.success) {
                        // Optionally refresh the reports section
                        location.reload();
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert(error.message);
                });
            }

            // Delete Confirmation Modal
            function showDeleteConfirmation(id, type, message, callback) {
                currentDeleteId = id;
                currentDeleteType = type;
                currentDeleteCallback = callback;
                document.getElementById('deleteModalMessage').textContent = message;
                document.getElementById('deleteModal').style.display = 'block';
            }

            function closeDeleteModal() {
                document.getElementById('deleteModal').style.display = 'none';
                currentDeleteId = null;
                currentDeleteType = null;
                currentDeleteCallback = null;
            }

            function confirmDelete() {
                if (currentDeleteCallback && currentDeleteId) {
                    currentDeleteCallback(currentDeleteId);
                }
                closeDeleteModal();
            }

            function confirmDeleteGig(id) {
                showDeleteConfirmation(id, 'gig', 'Are you sure you want to delete this gig?', deleteGig);
            }

            function confirmDeleteReport(id) {
                showDeleteConfirmation(id, 'report', 'Are you sure you want to delete this report?', deleteReport);
            }

            // Delete Functions
            function deleteClient(id) {
                fetch('/delete_client', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        client_id: id
                    })
                })
                .then(response => {
                    if (!response.ok) {
                        return response.json().then(data => {
                            throw new Error(data.error || 'Failed to delete client');
                        });
                    }
                    return response.json();
                })
                .then(data => {
                    if (data.success) {
                        location.reload();
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert(error.message);
                    location.reload();
                });
            }

            function deleteGig(id) {
                fetch('/delete_gig', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        gig_id: id
                    })
                })
                .then(response => {
                    if (!response.ok) {
                        return response.json().then(data => {
                            throw new Error(data.error || 'Failed to delete gig');
                        });
                    }
                    return response.json();
                })
                .then(data => {
                    if (data.success) {
                        location.reload();
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert(error.message);
                });
            }

            function deleteReport(id) {
                fetch('/delete_report', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        report_id: id
                    })
                })
                .then(response => {
                    if (!response.ok) {
                        return response.json().then(data => {
                            throw new Error(data.error || 'Failed to delete report');
                        });
                    }
                    return response.json();
                })
                .then(data => {
                    if (data.success) {
                        location.reload();
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert(error.message);
                });
            }

            // Logout Modal
            function openLogoutModal() {
                document.getElementById('logoutModal').style.display = 'block';
            }

            function closeLogoutModal() {
                document.getElementById('logoutModal').style.display = 'none';
            }

            function logout() {
                window.location.href = "{{ url_for('logout') }}";
            }

            // Date Filtering
            document.addEventListener('DOMContentLoaded', function() {
                const registrationFilter = document.getElementById('registrationFilter');
                const clientSearchInput = document.getElementById('clientSearchInput');
                const tableRows = document.querySelectorAll('#ClientsTab .table tbody tr');

                registrationFilter.addEventListener('change', filterClients);
                clientSearchInput.addEventListener('input', filterClients);

                function filterClients() {
                    const filterType = registrationFilter.value;
                    const searchTerm = clientSearchInput.value.toLowerCase();
                    const today = new Date();

                    tableRows.forEach(row => {
                        const createdAtCell = row.querySelector('td:nth-child(7)');
                        const nameCell = row.querySelector('td:nth-child(3)');
                        const businessCell = row.querySelector('td:nth-child(6)');

                        if (!createdAtCell || !nameCell || !businessCell) return;

                        const createdAt = new Date(createdAtCell.textContent);
                        const nameText = nameCell.textContent.toLowerCase();
                        const businessText = businessCell.textContent.toLowerCase();

                        let dateMatch = false;
                        switch(filterType) {
                            case 'daily':
                                dateMatch = isSameDay(createdAt, today);
                                break;
                            case 'weekly':
                                dateMatch = isWithinLastWeek(createdAt, today);
                                break;
                            case 'monthly':
                                dateMatch = isSameMonth(createdAt, today);
                                break;
                            case 'yearly':
                                dateMatch = isSameYear(createdAt, today);
                                break;
                            default:
                                dateMatch = true;
                        }

                        const searchMatch = searchTerm === '' ||
                                            nameText.includes(searchTerm) ||
                                            businessText.includes(searchTerm);

                        row.style.display = dateMatch && searchMatch ? '' : 'none';

                        // Highlight matching text
                        if (searchTerm !== '') {
                            highlightText(nameCell, searchTerm);
                            highlightText(businessCell, searchTerm);
                        } else {
                            removeHighlight(nameCell);
                            removeHighlight(businessCell);
                        }

                        // Highlight created at cell if filtered by date
                        if (filterType !== 'all') {
                            createdAtCell.classList.add('created-at-highlight');
                        } else {
                            createdAtCell.classList.remove('created-at-highlight');
                        }
                    });
                }

                function highlightText(element, term) {
                    const text = element.textContent;
                    const regex = new RegExp(term, 'gi');
                    const highlighted = text.replace(regex, match => `<span class="highlight">${match}</span>`);
                    element.innerHTML = highlighted;
                }

                function removeHighlight(element) {
                    element.innerHTML = element.textContent;
                }

                function isSameDay(date1, date2) {
                    return date1.getDate() === date2.getDate() &&
                           date1.getMonth() === date2.getMonth() &&
                           date1.getFullYear() === date2.getFullYear();
                }

                function isWithinLastWeek(date, today) {
                    const weekAgo = new Date(today);
                    weekAgo.setDate(weekAgo.getDate() - 7);
                    return date >= weekAgo && date <= today;
                }

                function isSameMonth(date1, date2) {
                    return date1.getMonth() === date2.getMonth() &&
                           date1.getFullYear() === date2.getFullYear();
                }

                function isSameYear(date1, date2) {
                    return date1.getFullYear() === date2.getFullYear();
                }
            });

            document.addEventListener('DOMContentLoaded', function() {
                // Charts initialization
                initializeCharts();
                // Show Dashboard tab by default
                showTab('Dashboard');
            });
        </script>
    </body>
</html>
